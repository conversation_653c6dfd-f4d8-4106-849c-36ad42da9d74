// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: cacheStatic transform > cache element with static key 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("div", { key: "foo" }, null, -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > cache nested children array 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("p", null, [
        _createElementVNode("span"),
        _createElementVNode("span")
      ], -1 /* HOISTED */),
      _createElementVNode("p", null, [
        _createElementVNode("span"),
        _createElementVNode("span")
      ], -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > cache nested static tree with comments 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createCommentVNode: _createCommentVNode, createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("div", null, [
        _createCommentVNode("comment")
      ], -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > cache siblings including text with common non-hoistable parent 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, createTextVNode: _createTextVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("span", null, null, -1 /* HOISTED */),
      _createTextVNode("foo"),
      _createElementVNode("div", null, null, -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > cache single children array 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("span", { class: "inline" }, "hello", -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > hoist static props for elements with directives 1`] = `
"const _Vue = Vue
const { createElementVNode: _createElementVNode } = _Vue

const _hoisted_1 = { id: "foo" }

return function render(_ctx, _cache) {
  with (_ctx) {
    const { resolveDirective: _resolveDirective, createElementVNode: _createElementVNode, withDirectives: _withDirectives, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    const _directive_foo = _resolveDirective("foo")

    return (_openBlock(), _createElementBlock("div", null, [
      _withDirectives(_createElementVNode("div", _hoisted_1, null, 512 /* NEED_PATCH */), [
        [_directive_foo]
      ])
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > hoist static props for elements with dynamic text children 1`] = `
"const _Vue = Vue
const { createElementVNode: _createElementVNode } = _Vue

const _hoisted_1 = { id: "foo" }

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      _createElementVNode("div", _hoisted_1, _toDisplayString(hello), 1 /* TEXT */)
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > hoist static props for elements with unhoistable children 1`] = `
"const _Vue = Vue
const { createVNode: _createVNode, createElementVNode: _createElementVNode } = _Vue

const _hoisted_1 = { id: "foo" }

return function render(_ctx, _cache) {
  with (_ctx) {
    const { resolveComponent: _resolveComponent, createVNode: _createVNode, createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    const _component_Comp = _resolveComponent("Comp")

    return (_openBlock(), _createElementBlock("div", null, [
      _createElementVNode("div", _hoisted_1, [
        _createVNode(_component_Comp)
      ])
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > cache nested static tree with static interpolation 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("span", null, "foo " + _toDisplayString(1) + " " + _toDisplayString(true), -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > cache nested static tree with static prop value 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, _cache[0] || (_cache[0] = [
      _createElementVNode("span", { foo: 0 }, _toDisplayString(1), -1 /* HOISTED */)
    ])))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > clone hoisted array children in v-for + HMR mode 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(1, (i) => {
        return (_openBlock(), _createElementBlock("div", null, [...(_cache[0] || (_cache[0] = [
          _createElementVNode("span", { class: "hi" }, null, -1 /* HOISTED */)
        ]))]))
      }), 256 /* UNKEYED_FRAGMENT */))
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > hoist class with static object value 1`] = `
"const _Vue = Vue
const { createElementVNode: _createElementVNode } = _Vue

const _hoisted_1 = {
  class: /*@__PURE__*/_normalizeClass({ foo: true })
}

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, normalizeClass: _normalizeClass, createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      _createElementVNode("span", _hoisted_1, _toDisplayString(_ctx.bar), 1 /* TEXT */)
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache SVG with directives 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, resolveDirective: _resolveDirective, openBlock: _openBlock, createElementBlock: _createElementBlock, withDirectives: _withDirectives } = _Vue

    const _directive_foo = _resolveDirective("foo")

    return (_openBlock(), _createElementBlock("div", null, [
      _withDirectives((_openBlock(), _createElementBlock("svg", null, _cache[0] || (_cache[0] = [
        _createElementVNode("path", { d: "M2,3H5.5L12" }, null, -1 /* HOISTED */)
      ]))), [
        [_directive_foo]
      ])
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache elements with cached handlers + other bindings 1`] = `
"import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("div", null, [
    _createElementVNode("div", null, [
      _createElementVNode("div", {
        class: _normalizeClass({}),
        onClick: _cache[0] || (_cache[0] = (...args) => (_ctx.foo && _ctx.foo(...args)))
      })
    ])
  ]))
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache elements with cached handlers 1`] = `
"import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue"

export function render(_ctx, _cache) {
  return (_openBlock(), _createElementBlock("div", null, [
    _createElementVNode("div", null, [
      _createElementVNode("div", {
        onClick: _cache[0] || (_cache[0] = (...args) => (_ctx.foo && _ctx.foo(...args)))
      })
    ])
  ]))
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache expressions that refer scope variables (2) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, toDisplayString: _toDisplayString, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.list, (o) => {
        return (_openBlock(), _createElementBlock("p", null, [
          _createElementVNode("span", null, _toDisplayString(o + 'foo'), 1 /* TEXT */)
        ]))
      }), 256 /* UNKEYED_FRAGMENT */))
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache expressions that refer scope variables (v-slot) 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { toDisplayString: _toDisplayString, createTextVNode: _createTextVNode, resolveComponent: _resolveComponent, withCtx: _withCtx, openBlock: _openBlock, createBlock: _createBlock } = _Vue

    const _component_Comp = _resolveComponent("Comp")

    return (_openBlock(), _createBlock(_component_Comp, null, {
      default: _withCtx(({ foo }) => [
        _createTextVNode(_toDisplayString(_ctx.foo), 1 /* TEXT */)
      ]),
      _: 1 /* STABLE */
    }))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache expressions that refer scope variables 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, toDisplayString: _toDisplayString, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.list, (o) => {
        return (_openBlock(), _createElementBlock("p", null, [
          _createElementVNode("span", null, _toDisplayString(o), 1 /* TEXT */)
        ]))
      }), 256 /* UNKEYED_FRAGMENT */))
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > prefixIdentifiers > should NOT cache keyed template v-for with plain element child 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(items, (item) => {
        return (_openBlock(), _createElementBlock("span", { key: item }))
      }), 128 /* KEYED_FRAGMENT */))
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > should NOT cache components 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { resolveComponent: _resolveComponent, createVNode: _createVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    const _component_Comp = _resolveComponent("Comp")

    return (_openBlock(), _createElementBlock("div", null, [
      _createVNode(_component_Comp)
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > should NOT cache element with dynamic key 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      (_openBlock(), _createElementBlock("div", { key: foo }))
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > should NOT cache element with dynamic props (but hoist the props list) 1`] = `
"const _Vue = Vue
const { createElementVNode: _createElementVNode } = _Vue

const _hoisted_1 = ["id"]

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      _createElementVNode("div", { id: foo }, null, 8 /* PROPS */, _hoisted_1)
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > should NOT cache element with dynamic ref 1`] = `
"const _Vue = Vue

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      _createElementVNode("div", { ref: foo }, null, 512 /* NEED_PATCH */)
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > should cache v-if props/children if static 1`] = `
"const _Vue = Vue
const { createElementVNode: _createElementVNode, createCommentVNode: _createCommentVNode } = _Vue

const _hoisted_1 = {
  key: 0,
  id: "foo"
}

return function render(_ctx, _cache) {
  with (_ctx) {
    const { createElementVNode: _createElementVNode, openBlock: _openBlock, createElementBlock: _createElementBlock, createCommentVNode: _createCommentVNode } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      ok
        ? (_openBlock(), _createElementBlock("div", _hoisted_1, _cache[0] || (_cache[0] = [
            _createElementVNode("span", null, null, -1 /* HOISTED */)
          ])))
        : _createCommentVNode("v-if", true)
    ]))
  }
}"
`;

exports[`compiler: cacheStatic transform > should hoist v-for children if static 1`] = `
"const _Vue = Vue
const { createElementVNode: _createElementVNode } = _Vue

const _hoisted_1 = { id: "foo" }

return function render(_ctx, _cache) {
  with (_ctx) {
    const { renderList: _renderList, Fragment: _Fragment, openBlock: _openBlock, createElementBlock: _createElementBlock, createElementVNode: _createElementVNode } = _Vue

    return (_openBlock(), _createElementBlock("div", null, [
      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(list, (i) => {
        return (_openBlock(), _createElementBlock("div", _hoisted_1, _cache[0] || (_cache[0] = [
          _createElementVNode("span", null, null, -1 /* HOISTED */)
        ])))
      }), 256 /* UNKEYED_FRAGMENT */))
    ]))
  }
}"
`;
