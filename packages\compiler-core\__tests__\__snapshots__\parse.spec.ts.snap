// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: parse > Edge Cases > invalid html 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 1,
              "line": 3,
              "offset": 13,
            },
            "source": "<span>
",
            "start": {
              "column": 1,
              "line": 2,
              "offset": 6,
            },
          },
          "ns": 0,
          "props": [],
          "tag": "span",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 7,
          "line": 3,
          "offset": 19,
        },
        "source": "<div>
<span>
</div>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "div",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 8,
      "line": 4,
      "offset": 27,
    },
    "source": "<div>
<span>
</div>
</span>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<div>
<span>
</div>
</span>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Edge Cases > self closing multiple tag 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "isSelfClosing": true,
      "loc": {
        "end": {
          "column": 37,
          "line": 1,
          "offset": 36,
        },
        "source": "<div :class="{ some: condition }" />",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [
        {
          "arg": {
            "constType": 3,
            "content": "class",
            "isStatic": true,
            "loc": {
              "end": {
                "column": 12,
                "line": 1,
                "offset": 11,
              },
              "source": "class",
              "start": {
                "column": 7,
                "line": 1,
                "offset": 6,
              },
            },
            "type": 4,
          },
          "exp": {
            "constType": 0,
            "content": "{ some: condition }",
            "isStatic": false,
            "loc": {
              "end": {
                "column": 33,
                "line": 1,
                "offset": 32,
              },
              "source": "{ some: condition }",
              "start": {
                "column": 14,
                "line": 1,
                "offset": 13,
              },
            },
            "type": 4,
          },
          "loc": {
            "end": {
              "column": 34,
              "line": 1,
              "offset": 33,
            },
            "source": ":class="{ some: condition }"",
            "start": {
              "column": 6,
              "line": 1,
              "offset": 5,
            },
          },
          "modifiers": [],
          "name": "bind",
          "rawName": ":class",
          "type": 7,
        },
      ],
      "tag": "div",
      "tagType": 0,
      "type": 1,
    },
    {
      "children": [],
      "codegenNode": undefined,
      "isSelfClosing": true,
      "loc": {
        "end": {
          "column": 37,
          "line": 2,
          "offset": 73,
        },
        "source": "<p v-bind:style="{ color: 'red' }"/>",
        "start": {
          "column": 1,
          "line": 2,
          "offset": 37,
        },
      },
      "ns": 0,
      "props": [
        {
          "arg": {
            "constType": 3,
            "content": "style",
            "isStatic": true,
            "loc": {
              "end": {
                "column": 16,
                "line": 2,
                "offset": 52,
              },
              "source": "style",
              "start": {
                "column": 11,
                "line": 2,
                "offset": 47,
              },
            },
            "type": 4,
          },
          "exp": {
            "constType": 0,
            "content": "{ color: 'red' }",
            "isStatic": false,
            "loc": {
              "end": {
                "column": 34,
                "line": 2,
                "offset": 70,
              },
              "source": "{ color: 'red' }",
              "start": {
                "column": 18,
                "line": 2,
                "offset": 54,
              },
            },
            "type": 4,
          },
          "loc": {
            "end": {
              "column": 35,
              "line": 2,
              "offset": 71,
            },
            "source": "v-bind:style="{ color: 'red' }"",
            "start": {
              "column": 4,
              "line": 2,
              "offset": 40,
            },
          },
          "modifiers": [],
          "name": "bind",
          "rawName": "v-bind:style",
          "type": 7,
        },
      ],
      "tag": "p",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 37,
      "line": 2,
      "offset": 73,
    },
    "source": "<div :class="{ some: condition }" />
<p v-bind:style="{ color: 'red' }"/>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<div :class="{ some: condition }" />
<p v-bind:style="{ color: 'red' }"/>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Edge Cases > valid html 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "isSelfClosing": true,
          "loc": {
            "end": {
              "column": 39,
              "line": 2,
              "offset": 73,
            },
            "source": "<p v-bind:style="{ color: 'red' }"/>",
            "start": {
              "column": 3,
              "line": 2,
              "offset": 37,
            },
          },
          "ns": 0,
          "props": [
            {
              "arg": {
                "constType": 3,
                "content": "style",
                "isStatic": true,
                "loc": {
                  "end": {
                    "column": 18,
                    "line": 2,
                    "offset": 52,
                  },
                  "source": "style",
                  "start": {
                    "column": 13,
                    "line": 2,
                    "offset": 47,
                  },
                },
                "type": 4,
              },
              "exp": {
                "constType": 0,
                "content": "{ color: 'red' }",
                "isStatic": false,
                "loc": {
                  "end": {
                    "column": 36,
                    "line": 2,
                    "offset": 70,
                  },
                  "source": "{ color: 'red' }",
                  "start": {
                    "column": 20,
                    "line": 2,
                    "offset": 54,
                  },
                },
                "type": 4,
              },
              "loc": {
                "end": {
                  "column": 37,
                  "line": 2,
                  "offset": 71,
                },
                "source": "v-bind:style="{ color: 'red' }"",
                "start": {
                  "column": 6,
                  "line": 2,
                  "offset": 40,
                },
              },
              "modifiers": [],
              "name": "bind",
              "rawName": "v-bind:style",
              "type": 7,
            },
          ],
          "tag": "p",
          "tagType": 0,
          "type": 1,
        },
        {
          "content": " a comment with <html> inside it ",
          "loc": {
            "end": {
              "column": 43,
              "line": 3,
              "offset": 116,
            },
            "source": "<!-- a comment with <html> inside it -->",
            "start": {
              "column": 3,
              "line": 3,
              "offset": 76,
            },
          },
          "type": 3,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 7,
          "line": 4,
          "offset": 123,
        },
        "source": "<div :class="{ some: condition }">
  <p v-bind:style="{ color: 'red' }"/>
  <!-- a comment with <html> inside it -->
</div>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [
        {
          "arg": {
            "constType": 3,
            "content": "class",
            "isStatic": true,
            "loc": {
              "end": {
                "column": 12,
                "line": 1,
                "offset": 11,
              },
              "source": "class",
              "start": {
                "column": 7,
                "line": 1,
                "offset": 6,
              },
            },
            "type": 4,
          },
          "exp": {
            "constType": 0,
            "content": "{ some: condition }",
            "isStatic": false,
            "loc": {
              "end": {
                "column": 33,
                "line": 1,
                "offset": 32,
              },
              "source": "{ some: condition }",
              "start": {
                "column": 14,
                "line": 1,
                "offset": 13,
              },
            },
            "type": 4,
          },
          "loc": {
            "end": {
              "column": 34,
              "line": 1,
              "offset": 33,
            },
            "source": ":class="{ some: condition }"",
            "start": {
              "column": 6,
              "line": 1,
              "offset": 5,
            },
          },
          "modifiers": [],
          "name": "bind",
          "rawName": ":class",
          "type": 7,
        },
      ],
      "tag": "div",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 7,
      "line": 4,
      "offset": 123,
    },
    "source": "<div :class="{ some: condition }">
  <p v-bind:style="{ color: 'red' }"/>
  <!-- a comment with <html> inside it -->
</div>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<div :class="{ some: condition }">
  <p v-bind:style="{ color: 'red' }"/>
  <!-- a comment with <html> inside it -->
</div>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > CDATA_IN_HTML_CONTENT > <template><![CDATA[cdata]]></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 39,
          "line": 1,
          "offset": 38,
        },
        "source": "<template><![CDATA[cdata]]></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 39,
      "line": 1,
      "offset": 38,
    },
    "source": "<template><![CDATA[cdata]]></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><![CDATA[cdata]]></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > CDATA_IN_HTML_CONTENT > <template><svg><![CDATA[cdata]]></svg></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [
            {
              "content": "cdata",
              "loc": {
                "end": {
                  "column": 30,
                  "line": 1,
                  "offset": 29,
                },
                "source": "cdata",
                "start": {
                  "column": 25,
                  "line": 1,
                  "offset": 24,
                },
              },
              "type": 2,
            },
          ],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 39,
              "line": 1,
              "offset": 38,
            },
            "source": "<svg><![CDATA[cdata]]></svg>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 1,
          "props": [],
          "tag": "svg",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 50,
          "line": 1,
          "offset": 49,
        },
        "source": "<template><svg><![CDATA[cdata]]></svg></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 50,
      "line": 1,
      "offset": 49,
    },
    "source": "<template><svg><![CDATA[cdata]]></svg></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><svg><![CDATA[cdata]]></svg></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > DUPLICATE_ATTRIBUTE > <template><div id="" id=""></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 34,
              "line": 1,
              "offset": 33,
            },
            "source": "<div id="" id=""></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 21,
                  "line": 1,
                  "offset": 20,
                },
                "source": "id=""",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "id",
              "nameLoc": {
                "end": {
                  "column": 18,
                  "line": 1,
                  "offset": 17,
                },
                "source": "id",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 21,
                    "line": 1,
                    "offset": 20,
                  },
                  "source": """",
                  "start": {
                    "column": 19,
                    "line": 1,
                    "offset": 18,
                  },
                },
                "type": 2,
              },
            },
            {
              "loc": {
                "end": {
                  "column": 27,
                  "line": 1,
                  "offset": 26,
                },
                "source": "id=""",
                "start": {
                  "column": 22,
                  "line": 1,
                  "offset": 21,
                },
              },
              "name": "id",
              "nameLoc": {
                "end": {
                  "column": 24,
                  "line": 1,
                  "offset": 23,
                },
                "source": "id",
                "start": {
                  "column": 22,
                  "line": 1,
                  "offset": 21,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 27,
                    "line": 1,
                    "offset": 26,
                  },
                  "source": """",
                  "start": {
                    "column": 25,
                    "line": 1,
                    "offset": 24,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 45,
          "line": 1,
          "offset": 44,
        },
        "source": "<template><div id="" id=""></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 45,
      "line": 1,
      "offset": 44,
    },
    "source": "<template><div id="" id=""></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id="" id=""></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_BEFORE_TAG_NAME > <template>< 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "<",
          "loc": {
            "end": {
              "column": 12,
              "line": 1,
              "offset": 11,
            },
            "source": "<",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 12,
          "line": 1,
          "offset": 11,
        },
        "source": "<template><",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 12,
      "line": 1,
      "offset": 11,
    },
    "source": "<template><",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_BEFORE_TAG_NAME > <template></ 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "</",
          "loc": {
            "end": {
              "column": 13,
              "line": 1,
              "offset": 12,
            },
            "source": "</",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 13,
          "line": 1,
          "offset": 12,
        },
        "source": "<template></",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 13,
      "line": 1,
      "offset": 12,
    },
    "source": "<template></",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template></",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_CDATA > <template><svg><![CDATA[ 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 25,
              "line": 1,
              "offset": 24,
            },
            "source": "<svg><![CDATA[",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 1,
          "props": [],
          "tag": "svg",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 25,
          "line": 1,
          "offset": 24,
        },
        "source": "<template><svg><![CDATA[",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 25,
      "line": 1,
      "offset": 24,
    },
    "source": "<template><svg><![CDATA[",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><svg><![CDATA[",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_CDATA > <template><svg><![CDATA[cdata 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [
            {
              "content": "cdata",
              "loc": {
                "end": {
                  "column": 30,
                  "line": 1,
                  "offset": 29,
                },
                "source": "cdata",
                "start": {
                  "column": 25,
                  "line": 1,
                  "offset": 24,
                },
              },
              "type": 2,
            },
          ],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 30,
              "line": 1,
              "offset": 29,
            },
            "source": "<svg><![CDATA[cdata",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 1,
          "props": [],
          "tag": "svg",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 30,
          "line": 1,
          "offset": 29,
        },
        "source": "<template><svg><![CDATA[cdata",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 30,
      "line": 1,
      "offset": 29,
    },
    "source": "<template><svg><![CDATA[cdata",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><svg><![CDATA[cdata",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_COMMENT > <template><!-- 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 15,
          "line": 1,
          "offset": 14,
        },
        "source": "<template><!--",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 15,
      "line": 1,
      "offset": 14,
    },
    "source": "<template><!--",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><!--",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_COMMENT > <template><!--comment 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "comment",
          "loc": {
            "end": {
              "column": 25,
              "line": 1,
              "offset": 24,
            },
            "source": "<!--comment",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "type": 3,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 22,
          "line": 1,
          "offset": 21,
        },
        "source": "<template><!--comment",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 22,
      "line": 1,
      "offset": 21,
    },
    "source": "<template><!--comment",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><!--comment",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <div></div 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 11,
          "line": 1,
          "offset": 10,
        },
        "source": "<div></div",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "div",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 11,
      "line": 1,
      "offset": 10,
    },
    "source": "<div></div",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<div></div",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div  1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 16,
          "line": 1,
          "offset": 15,
        },
        "source": "<template><div ",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 16,
      "line": 1,
      "offset": 15,
    },
    "source": "<template><div ",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div ",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 15,
          "line": 1,
          "offset": 14,
        },
        "source": "<template><div",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 15,
      "line": 1,
      "offset": 14,
    },
    "source": "<template><div",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id  1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 19,
          "line": 1,
          "offset": 18,
        },
        "source": "<template><div id ",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 19,
      "line": 1,
      "offset": 18,
    },
    "source": "<template><div id ",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id ",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id = 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 20,
          "line": 1,
          "offset": 19,
        },
        "source": "<template><div id =",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 20,
      "line": 1,
      "offset": 19,
    },
    "source": "<template><div id =",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id =",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 18,
          "line": 1,
          "offset": 17,
        },
        "source": "<template><div id",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 18,
      "line": 1,
      "offset": 17,
    },
    "source": "<template><div id",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id="abc 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 23,
          "line": 1,
          "offset": 22,
        },
        "source": "<template><div id="abc",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 23,
      "line": 1,
      "offset": 22,
    },
    "source": "<template><div id="abc",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id="abc",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id="abc" 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 24,
          "line": 1,
          "offset": 23,
        },
        "source": "<template><div id="abc"",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 24,
      "line": 1,
      "offset": 23,
    },
    "source": "<template><div id="abc"",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id="abc"",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id="abc"/ 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "/",
          "loc": {
            "end": {
              "column": 25,
              "line": 1,
              "offset": 24,
            },
            "source": "/",
            "start": {
              "column": 0,
              "line": 1,
              "offset": -1,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 25,
          "line": 1,
          "offset": 24,
        },
        "source": "<template><div id="abc"/",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 25,
      "line": 1,
      "offset": 24,
    },
    "source": "<template><div id="abc"/",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id="abc"/",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id='abc 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 23,
          "line": 1,
          "offset": 22,
        },
        "source": "<template><div id='abc",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 23,
      "line": 1,
      "offset": 22,
    },
    "source": "<template><div id='abc",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id='abc",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id='abc' 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 24,
          "line": 1,
          "offset": 23,
        },
        "source": "<template><div id='abc'",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 24,
      "line": 1,
      "offset": 23,
    },
    "source": "<template><div id='abc'",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id='abc'",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id='abc'/ 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "/",
          "loc": {
            "end": {
              "column": 25,
              "line": 1,
              "offset": 24,
            },
            "source": "/",
            "start": {
              "column": 0,
              "line": 1,
              "offset": -1,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 25,
          "line": 1,
          "offset": 24,
        },
        "source": "<template><div id='abc'/",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 25,
      "line": 1,
      "offset": 24,
    },
    "source": "<template><div id='abc'/",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id='abc'/",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id=abc / 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "/",
          "loc": {
            "end": {
              "column": 24,
              "line": 1,
              "offset": 23,
            },
            "source": "/",
            "start": {
              "column": 0,
              "line": 1,
              "offset": -1,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 24,
          "line": 1,
          "offset": 23,
        },
        "source": "<template><div id=abc /",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 24,
      "line": 1,
      "offset": 23,
    },
    "source": "<template><div id=abc /",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id=abc /",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > EOF_IN_TAG > <template><div id=abc 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 22,
          "line": 1,
          "offset": 21,
        },
        "source": "<template><div id=abc",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 22,
      "line": 1,
      "offset": 21,
    },
    "source": "<template><div id=abc",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id=abc",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > MISSING_ATTRIBUTE_VALUE > <template><div id= /></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 28,
              "line": 1,
              "offset": 27,
            },
            "source": "<div id= /></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 21,
                  "line": 1,
                  "offset": 20,
                },
                "source": "id= /",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "id",
              "nameLoc": {
                "end": {
                  "column": 18,
                  "line": 1,
                  "offset": 17,
                },
                "source": "id",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "/",
                "loc": {
                  "end": {
                    "column": 21,
                    "line": 1,
                    "offset": 20,
                  },
                  "source": "/",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 39,
          "line": 1,
          "offset": 38,
        },
        "source": "<template><div id= /></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 39,
      "line": 1,
      "offset": 38,
    },
    "source": "<template><div id= /></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id= /></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > MISSING_ATTRIBUTE_VALUE > <template><div id= ></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 27,
              "line": 1,
              "offset": 26,
            },
            "source": "<div id= ></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 20,
                  "line": 1,
                  "offset": 19,
                },
                "source": "id= ",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "id",
              "nameLoc": {
                "end": {
                  "column": 18,
                  "line": 1,
                  "offset": 17,
                },
                "source": "id",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                  "source": "",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 38,
          "line": 1,
          "offset": 37,
        },
        "source": "<template><div id= ></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 38,
      "line": 1,
      "offset": 37,
    },
    "source": "<template><div id= ></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id= ></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > MISSING_ATTRIBUTE_VALUE > <template><div id=></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 26,
              "line": 1,
              "offset": 25,
            },
            "source": "<div id=></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "id=",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "id",
              "nameLoc": {
                "end": {
                  "column": 18,
                  "line": 1,
                  "offset": 17,
                },
                "source": "id",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 19,
                    "line": 1,
                    "offset": 18,
                  },
                  "source": "",
                  "start": {
                    "column": 19,
                    "line": 1,
                    "offset": 18,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 37,
          "line": 1,
          "offset": 36,
        },
        "source": "<template><div id=></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 37,
      "line": 1,
      "offset": 36,
    },
    "source": "<template><div id=></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div id=></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > MISSING_END_TAG_NAME > <template></></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 25,
          "line": 1,
          "offset": 24,
        },
        "source": "<template></></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 25,
      "line": 1,
      "offset": 24,
    },
    "source": "<template></></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template></></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME > <template><div a"bc=''></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 30,
              "line": 1,
              "offset": 29,
            },
            "source": "<div a"bc=''></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 23,
                  "line": 1,
                  "offset": 22,
                },
                "source": "a"bc=''",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "a"bc",
              "nameLoc": {
                "end": {
                  "column": 20,
                  "line": 1,
                  "offset": 19,
                },
                "source": "a"bc",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 23,
                    "line": 1,
                    "offset": 22,
                  },
                  "source": "''",
                  "start": {
                    "column": 21,
                    "line": 1,
                    "offset": 20,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 41,
          "line": 1,
          "offset": 40,
        },
        "source": "<template><div a"bc=''></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 41,
      "line": 1,
      "offset": 40,
    },
    "source": "<template><div a"bc=''></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div a"bc=''></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME > <template><div a'bc=''></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 30,
              "line": 1,
              "offset": 29,
            },
            "source": "<div a'bc=''></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 23,
                  "line": 1,
                  "offset": 22,
                },
                "source": "a'bc=''",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "a'bc",
              "nameLoc": {
                "end": {
                  "column": 20,
                  "line": 1,
                  "offset": 19,
                },
                "source": "a'bc",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 23,
                    "line": 1,
                    "offset": 22,
                  },
                  "source": "''",
                  "start": {
                    "column": 21,
                    "line": 1,
                    "offset": 20,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 41,
          "line": 1,
          "offset": 40,
        },
        "source": "<template><div a'bc=''></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 41,
      "line": 1,
      "offset": 40,
    },
    "source": "<template><div a'bc=''></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div a'bc=''></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME > <template><div a<bc=''></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 30,
              "line": 1,
              "offset": 29,
            },
            "source": "<div a<bc=''></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 23,
                  "line": 1,
                  "offset": 22,
                },
                "source": "a<bc=''",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "a<bc",
              "nameLoc": {
                "end": {
                  "column": 20,
                  "line": 1,
                  "offset": 19,
                },
                "source": "a<bc",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "",
                "loc": {
                  "end": {
                    "column": 23,
                    "line": 1,
                    "offset": 22,
                  },
                  "source": "''",
                  "start": {
                    "column": 21,
                    "line": 1,
                    "offset": 20,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 41,
          "line": 1,
          "offset": 40,
        },
        "source": "<template><div a<bc=''></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 41,
      "line": 1,
      "offset": 40,
    },
    "source": "<template><div a<bc=''></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div a<bc=''></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE > <template><div foo=bar"></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 31,
              "line": 1,
              "offset": 30,
            },
            "source": "<div foo=bar"></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 24,
                  "line": 1,
                  "offset": 23,
                },
                "source": "foo=bar"",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "foo",
              "nameLoc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "foo",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "bar"",
                "loc": {
                  "end": {
                    "column": 24,
                    "line": 1,
                    "offset": 23,
                  },
                  "source": "bar"",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 42,
          "line": 1,
          "offset": 41,
        },
        "source": "<template><div foo=bar"></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 42,
      "line": 1,
      "offset": 41,
    },
    "source": "<template><div foo=bar"></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div foo=bar"></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE > <template><div foo=bar'></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 31,
              "line": 1,
              "offset": 30,
            },
            "source": "<div foo=bar'></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 24,
                  "line": 1,
                  "offset": 23,
                },
                "source": "foo=bar'",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "foo",
              "nameLoc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "foo",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "bar'",
                "loc": {
                  "end": {
                    "column": 24,
                    "line": 1,
                    "offset": 23,
                  },
                  "source": "bar'",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 42,
          "line": 1,
          "offset": 41,
        },
        "source": "<template><div foo=bar'></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 42,
      "line": 1,
      "offset": 41,
    },
    "source": "<template><div foo=bar'></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div foo=bar'></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE > <template><div foo=bar<div></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 34,
              "line": 1,
              "offset": 33,
            },
            "source": "<div foo=bar<div></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 27,
                  "line": 1,
                  "offset": 26,
                },
                "source": "foo=bar<div",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "foo",
              "nameLoc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "foo",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "bar<div",
                "loc": {
                  "end": {
                    "column": 27,
                    "line": 1,
                    "offset": 26,
                  },
                  "source": "bar<div",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 45,
          "line": 1,
          "offset": 44,
        },
        "source": "<template><div foo=bar<div></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 45,
      "line": 1,
      "offset": 44,
    },
    "source": "<template><div foo=bar<div></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div foo=bar<div></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE > <template><div foo=bar=baz></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 34,
              "line": 1,
              "offset": 33,
            },
            "source": "<div foo=bar=baz></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 27,
                  "line": 1,
                  "offset": 26,
                },
                "source": "foo=bar=baz",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "foo",
              "nameLoc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "foo",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "bar=baz",
                "loc": {
                  "end": {
                    "column": 27,
                    "line": 1,
                    "offset": 26,
                  },
                  "source": "bar=baz",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 45,
          "line": 1,
          "offset": 44,
        },
        "source": "<template><div foo=bar=baz></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 45,
      "line": 1,
      "offset": 44,
    },
    "source": "<template><div foo=bar=baz></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div foo=bar=baz></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE > <template><div foo=bar\`></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 31,
              "line": 1,
              "offset": 30,
            },
            "source": "<div foo=bar\`></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 24,
                  "line": 1,
                  "offset": 23,
                },
                "source": "foo=bar\`",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "foo",
              "nameLoc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "foo",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "bar\`",
                "loc": {
                  "end": {
                    "column": 24,
                    "line": 1,
                    "offset": 23,
                  },
                  "source": "bar\`",
                  "start": {
                    "column": 20,
                    "line": 1,
                    "offset": 19,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 42,
          "line": 1,
          "offset": 41,
        },
        "source": "<template><div foo=bar\`></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 42,
      "line": 1,
      "offset": 41,
    },
    "source": "<template><div foo=bar\`></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div foo=bar\`></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME > <template><div =></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 24,
              "line": 1,
              "offset": 23,
            },
            "source": "<div =></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 17,
                  "line": 1,
                  "offset": 16,
                },
                "source": "=",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "=",
              "nameLoc": {
                "end": {
                  "column": 17,
                  "line": 1,
                  "offset": 16,
                },
                "source": "=",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": undefined,
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 35,
          "line": 1,
          "offset": 34,
        },
        "source": "<template><div =></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 35,
      "line": 1,
      "offset": 34,
    },
    "source": "<template><div =></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div =></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME > <template><div =foo=bar></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 31,
              "line": 1,
              "offset": 30,
            },
            "source": "<div =foo=bar></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 24,
                  "line": 1,
                  "offset": 23,
                },
                "source": "=foo=bar",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "=foo",
              "nameLoc": {
                "end": {
                  "column": 20,
                  "line": 1,
                  "offset": 19,
                },
                "source": "=foo",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": {
                "content": "bar",
                "loc": {
                  "end": {
                    "column": 24,
                    "line": 1,
                    "offset": 23,
                  },
                  "source": "bar",
                  "start": {
                    "column": 21,
                    "line": 1,
                    "offset": 20,
                  },
                },
                "type": 2,
              },
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 42,
          "line": 1,
          "offset": 41,
        },
        "source": "<template><div =foo=bar></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 42,
      "line": 1,
      "offset": 41,
    },
    "source": "<template><div =foo=bar></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div =foo=bar></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME > <template><?xml?></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 29,
          "line": 1,
          "offset": 28,
        },
        "source": "<template><?xml?></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 29,
      "line": 1,
      "offset": 28,
    },
    "source": "<template><?xml?></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><?xml?></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > UNEXPECTED_SOLIDUS_IN_TAG > <template><div a/b></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 26,
              "line": 1,
              "offset": 25,
            },
            "source": "<div a/b></div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [
            {
              "loc": {
                "end": {
                  "column": 17,
                  "line": 1,
                  "offset": 16,
                },
                "source": "a",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "name": "a",
              "nameLoc": {
                "end": {
                  "column": 17,
                  "line": 1,
                  "offset": 16,
                },
                "source": "a",
                "start": {
                  "column": 16,
                  "line": 1,
                  "offset": 15,
                },
              },
              "type": 6,
              "value": undefined,
            },
            {
              "loc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "b",
                "start": {
                  "column": 18,
                  "line": 1,
                  "offset": 17,
                },
              },
              "name": "b",
              "nameLoc": {
                "end": {
                  "column": 19,
                  "line": 1,
                  "offset": 18,
                },
                "source": "b",
                "start": {
                  "column": 18,
                  "line": 1,
                  "offset": 17,
                },
              },
              "type": 6,
              "value": undefined,
            },
          ],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 37,
          "line": 1,
          "offset": 36,
        },
        "source": "<template><div a/b></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 37,
      "line": 1,
      "offset": 36,
    },
    "source": "<template><div a/b></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div a/b></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <svg><![CDATA[</div>]]></svg> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "</div>",
          "loc": {
            "end": {
              "column": 21,
              "line": 1,
              "offset": 20,
            },
            "source": "</div>",
            "start": {
              "column": 15,
              "line": 1,
              "offset": 14,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 30,
          "line": 1,
          "offset": 29,
        },
        "source": "<svg><![CDATA[</div>]]></svg>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 1,
      "props": [],
      "tag": "svg",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 30,
      "line": 1,
      "offset": 29,
    },
    "source": "<svg><![CDATA[</div>]]></svg>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<svg><![CDATA[</div>]]></svg>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <svg><!--</div>--></svg> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "</div>",
          "loc": {
            "end": {
              "column": 19,
              "line": 1,
              "offset": 18,
            },
            "source": "<!--</div>-->",
            "start": {
              "column": 6,
              "line": 1,
              "offset": 5,
            },
          },
          "type": 3,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 25,
          "line": 1,
          "offset": 24,
        },
        "source": "<svg><!--</div>--></svg>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 1,
      "props": [],
      "tag": "svg",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 25,
      "line": 1,
      "offset": 24,
    },
    "source": "<svg><!--</div>--></svg>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<svg><!--</div>--></svg>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <template></div></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 34,
          "line": 1,
          "offset": 33,
        },
        "source": "<template></div></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 34,
      "line": 1,
      "offset": 33,
    },
    "source": "<template></div></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template></div></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <template></div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 28,
          "line": 1,
          "offset": 27,
        },
        "source": "<template></div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 28,
      "line": 1,
      "offset": 27,
    },
    "source": "<template></div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template></div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <template>{{'</div>'}}</template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": {
            "constType": 0,
            "content": "'</div>'",
            "isStatic": false,
            "loc": {
              "end": {
                "column": 21,
                "line": 1,
                "offset": 20,
              },
              "source": "'</div>'",
              "start": {
                "column": 13,
                "line": 1,
                "offset": 12,
              },
            },
            "type": 4,
          },
          "loc": {
            "end": {
              "column": 23,
              "line": 1,
              "offset": 22,
            },
            "source": "{{'</div>'}}",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "type": 5,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 34,
          "line": 1,
          "offset": 33,
        },
        "source": "<template>{{'</div>'}}</template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 34,
      "line": 1,
      "offset": 33,
    },
    "source": "<template>{{'</div>'}}</template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template>{{'</div>'}}</template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <template>a </ b</template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "a ",
          "loc": {
            "end": {
              "column": 13,
              "line": 1,
              "offset": 12,
            },
            "source": "a ",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 28,
          "line": 1,
          "offset": 27,
        },
        "source": "<template>a </ b</template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 28,
      "line": 1,
      "offset": 27,
    },
    "source": "<template>a </ b</template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template>a </ b</template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_INVALID_END_TAG > <textarea></div></textarea> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "</div>",
          "loc": {
            "end": {
              "column": 17,
              "line": 1,
              "offset": 16,
            },
            "source": "</div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 28,
          "line": 1,
          "offset": 27,
        },
        "source": "<textarea></div></textarea>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "textarea",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 28,
      "line": 1,
      "offset": 27,
    },
    "source": "<textarea></div></textarea>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<textarea></div></textarea>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END > <div v-foo:[sef fsef] /> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [],
      "codegenNode": undefined,
      "isSelfClosing": true,
      "loc": {
        "end": {
          "column": 25,
          "line": 1,
          "offset": 24,
        },
        "source": "<div v-foo:[sef fsef] />",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [
        {
          "arg": {
            "constType": 0,
            "content": "sef",
            "isStatic": false,
            "loc": {
              "end": {
                "column": 17,
                "line": 1,
                "offset": 16,
              },
              "source": "[sef ",
              "start": {
                "column": 12,
                "line": 1,
                "offset": 11,
              },
            },
            "type": 4,
          },
          "exp": undefined,
          "loc": {
            "end": {
              "column": 16,
              "line": 1,
              "offset": 15,
            },
            "source": "v-foo:[sef",
            "start": {
              "column": 6,
              "line": 1,
              "offset": 5,
            },
          },
          "modifiers": [],
          "name": "foo",
          "rawName": "v-foo:[sef",
          "type": 7,
        },
        {
          "loc": {
            "end": {
              "column": 22,
              "line": 1,
              "offset": 21,
            },
            "source": "fsef]",
            "start": {
              "column": 17,
              "line": 1,
              "offset": 16,
            },
          },
          "name": "fsef]",
          "nameLoc": {
            "end": {
              "column": 22,
              "line": 1,
              "offset": 21,
            },
            "source": "fsef]",
            "start": {
              "column": 17,
              "line": 1,
              "offset": 16,
            },
          },
          "type": 6,
          "value": undefined,
        },
      ],
      "tag": "div",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 25,
      "line": 1,
      "offset": 24,
    },
    "source": "<div v-foo:[sef fsef] />",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<div v-foo:[sef fsef] />",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_END_TAG > <template><div> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 16,
              "line": 1,
              "offset": 15,
            },
            "source": "<div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 16,
          "line": 1,
          "offset": 15,
        },
        "source": "<template><div>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 16,
      "line": 1,
      "offset": 15,
    },
    "source": "<template><div>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_END_TAG > <template><div></template> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "children": [],
          "codegenNode": undefined,
          "loc": {
            "end": {
              "column": 16,
              "line": 1,
              "offset": 15,
            },
            "source": "<div>",
            "start": {
              "column": 11,
              "line": 1,
              "offset": 10,
            },
          },
          "ns": 0,
          "props": [],
          "tag": "div",
          "tagType": 0,
          "type": 1,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 27,
          "line": 1,
          "offset": 26,
        },
        "source": "<template><div></template>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "template",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 27,
      "line": 1,
      "offset": 26,
    },
    "source": "<template><div></template>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<template><div></template>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_INTERPOLATION_END > <div>{{ foo</div> 1`] = `
{
  "cached": [],
  "children": [
    {
      "children": [
        {
          "content": "{{ foo</div>",
          "loc": {
            "end": {
              "column": 18,
              "line": 1,
              "offset": 17,
            },
            "source": "{{ foo</div>",
            "start": {
              "column": 6,
              "line": 1,
              "offset": 5,
            },
          },
          "type": 2,
        },
      ],
      "codegenNode": undefined,
      "loc": {
        "end": {
          "column": 18,
          "line": 1,
          "offset": 17,
        },
        "source": "<div>{{ foo</div>",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "ns": 0,
      "props": [],
      "tag": "div",
      "tagType": 0,
      "type": 1,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 18,
      "line": 1,
      "offset": 17,
    },
    "source": "<div>{{ foo</div>",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "<div>{{ foo</div>",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_INTERPOLATION_END > {{ 1`] = `
{
  "cached": [],
  "children": [
    {
      "content": "{{",
      "loc": {
        "end": {
          "column": 3,
          "line": 1,
          "offset": 2,
        },
        "source": "{{",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "type": 2,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 3,
      "line": 1,
      "offset": 2,
    },
    "source": "{{",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "{{",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_INTERPOLATION_END > {{ foo 1`] = `
{
  "cached": [],
  "children": [
    {
      "content": "{{ foo",
      "loc": {
        "end": {
          "column": 7,
          "line": 1,
          "offset": 6,
        },
        "source": "{{ foo",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "type": 2,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 7,
      "line": 1,
      "offset": 6,
    },
    "source": "{{ foo",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "{{ foo",
  "temps": 0,
  "type": 0,
}
`;

exports[`compiler: parse > Errors > X_MISSING_INTERPOLATION_END > {{}} 1`] = `
{
  "cached": [],
  "children": [
    {
      "content": {
        "constType": 0,
        "content": "",
        "isStatic": false,
        "loc": {
          "end": {
            "column": 3,
            "line": 1,
            "offset": 2,
          },
          "source": "",
          "start": {
            "column": 3,
            "line": 1,
            "offset": 2,
          },
        },
        "type": 4,
      },
      "loc": {
        "end": {
          "column": 5,
          "line": 1,
          "offset": 4,
        },
        "source": "{{}}",
        "start": {
          "column": 1,
          "line": 1,
          "offset": 0,
        },
      },
      "type": 5,
    },
  ],
  "codegenNode": undefined,
  "components": [],
  "directives": [],
  "helpers": Set {},
  "hoists": [],
  "imports": [],
  "loc": {
    "end": {
      "column": 5,
      "line": 1,
      "offset": 4,
    },
    "source": "{{}}",
    "start": {
      "column": 1,
      "line": 1,
      "offset": 0,
    },
  },
  "source": "{{}}",
  "temps": 0,
  "type": 0,
}
`;
