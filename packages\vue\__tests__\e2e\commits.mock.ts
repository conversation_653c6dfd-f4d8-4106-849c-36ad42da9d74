export default {
  main: [
    {
      sha: 'd1527fbee422c7170e56845e55b49c4fd6de72a7',
      node_id:
        'MDY6Q29tbWl0MTM3MDc4NDg3OmQxNTI3ZmJlZTQyMmM3MTcwZTU2ODQ1ZTU1YjQ5YzRmZDZkZTcyYTc=',
      commit: {
        author: {
          name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>',
          email: 'haoq<PERSON><EMAIL>',
          date: '2019-12-09T19:52:20Z',
        },
        committer: {
          name: '<PERSON>',
          email: '<EMAIL>',
          date: '2019-12-09T19:52:20Z',
        },
        message: 'test: add test for runtime-dom/modules/class (#75)',
        tree: {
          sha: 'f53f761827af281db86c31d113086c068c1d0789',
          url: 'https://api.github.com/repos/vuejs/core/git/trees/f53f761827af281db86c31d113086c068c1d0789',
        },
        url: 'https://api.github.com/repos/vuejs/core/git/commits/d1527fbee422c7170e56845e55b49c4fd6de72a7',
        comment_count: 0,
        verification: {
          verified: false,
          reason: 'unsigned',
          signature: null,
          payload: null,
        },
      },
      url: 'https://api.github.com/repos/vuejs/core/commits/d1527fbee422c7170e56845e55b49c4fd6de72a7',
      html_url:
        'https://github.com/vuejs/core/commit/d1527fbee422c7170e56845e55b49c4fd6de72a7',
      comments_url:
        'https://api.github.com/repos/vuejs/core/commits/d1527fbee422c7170e56845e55b49c4fd6de72a7/comments',
      author: {
        login: 'sodatea',
        id: 3277634,
        node_id: 'MDQ6VXNlcjMyNzc2MzQ=',
        avatar_url: 'https://avatars3.githubusercontent.com/u/3277634?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/sodatea',
        html_url: 'https://github.com/sodatea',
        followers_url: 'https://api.github.com/users/sodatea/followers',
        following_url:
          'https://api.github.com/users/sodatea/following{/other_user}',
        gists_url: 'https://api.github.com/users/sodatea/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/sodatea/starred{/owner}{/repo}',
        subscriptions_url: 'https://api.github.com/users/sodatea/subscriptions',
        organizations_url: 'https://api.github.com/users/sodatea/orgs',
        repos_url: 'https://api.github.com/users/sodatea/repos',
        events_url: 'https://api.github.com/users/sodatea/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/sodatea/received_events',
        type: 'User',
        site_admin: false,
      },
      committer: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      parents: [
        {
          sha: '2383b45e322272ddc102d6914c149b284a25d04f',
          url: 'https://api.github.com/repos/vuejs/core/commits/2383b45e322272ddc102d6914c149b284a25d04f',
          html_url:
            'https://github.com/vuejs/core/commit/2383b45e322272ddc102d6914c149b284a25d04f',
        },
      ],
    },
    {
      sha: '2383b45e322272ddc102d6914c149b284a25d04f',
      node_id:
        'MDY6Q29tbWl0MTM3MDc4NDg3OjIzODNiNDVlMzIyMjcyZGRjMTAyZDY5MTRjMTQ5YjI4NGEyNWQwNGY=',
      commit: {
        author: {
          name: 'GCA',
          email: '<EMAIL>',
          date: '2019-12-09T19:23:57Z',
        },
        committer: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2019-12-09T19:23:57Z',
        },
        message: 'chore: fix typo (#530) [ci skip]',
        tree: {
          sha: '2a5872ff8dc8ccb8121abd7e890ac3c0c9f1209f',
          url: 'https://api.github.com/repos/vuejs/core/git/trees/2a5872ff8dc8ccb8121abd7e890ac3c0c9f1209f',
        },
        url: 'https://api.github.com/repos/vuejs/core/git/commits/2383b45e322272ddc102d6914c149b284a25d04f',
        comment_count: 0,
        verification: {
          verified: false,
          reason: 'unsigned',
          signature: null,
          payload: null,
        },
      },
      url: 'https://api.github.com/repos/vuejs/core/commits/2383b45e322272ddc102d6914c149b284a25d04f',
      html_url:
        'https://github.com/vuejs/core/commit/2383b45e322272ddc102d6914c149b284a25d04f',
      comments_url:
        'https://api.github.com/repos/vuejs/core/commits/2383b45e322272ddc102d6914c149b284a25d04f/comments',
      author: {
        login: 'gcaaa31928',
        id: 6309392,
        node_id: 'MDQ6VXNlcjYzMDkzOTI=',
        avatar_url: 'https://avatars1.githubusercontent.com/u/6309392?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/gcaaa31928',
        html_url: 'https://github.com/gcaaa31928',
        followers_url: 'https://api.github.com/users/gcaaa31928/followers',
        following_url:
          'https://api.github.com/users/gcaaa31928/following{/other_user}',
        gists_url: 'https://api.github.com/users/gcaaa31928/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/gcaaa31928/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/gcaaa31928/subscriptions',
        organizations_url: 'https://api.github.com/users/gcaaa31928/orgs',
        repos_url: 'https://api.github.com/users/gcaaa31928/repos',
        events_url: 'https://api.github.com/users/gcaaa31928/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/gcaaa31928/received_events',
        type: 'User',
        site_admin: false,
      },
      committer: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      parents: [
        {
          sha: 'e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
          url: 'https://api.github.com/repos/vuejs/core/commits/e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
          html_url:
            'https://github.com/vuejs/core/commit/e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
        },
      ],
    },
    {
      sha: 'e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
      node_id:
        'MDY6Q29tbWl0MTM3MDc4NDg3OmU3ZTEzMTRjY2NkMWE2NmZjZjhiODUyNmVjMjEzNTBlYzE2Y2MzYWQ=',
      commit: {
        author: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2019-12-09T19:23:01Z',
        },
        committer: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2019-12-09T19:23:01Z',
        },
        message: 'test: fix warning',
        tree: {
          sha: 'd942b17681e2e2fbbcd2ee04092390c7f2cf534d',
          url: 'https://api.github.com/repos/vuejs/core/git/trees/d942b17681e2e2fbbcd2ee04092390c7f2cf534d',
        },
        url: 'https://api.github.com/repos/vuejs/core/git/commits/e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
        comment_count: 0,
        verification: {
          verified: false,
          reason: 'unsigned',
          signature: null,
          payload: null,
        },
      },
      url: 'https://api.github.com/repos/vuejs/core/commits/e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
      html_url:
        'https://github.com/vuejs/core/commit/e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad',
      comments_url:
        'https://api.github.com/repos/vuejs/core/commits/e7e1314cccd1a66fcf8b8526ec21350ec16cc3ad/comments',
      author: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      committer: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      parents: [
        {
          sha: '12ec62e6881f83dfa6c7f8a3c3650ec2567e6b1e',
          url: 'https://api.github.com/repos/vuejs/core/commits/12ec62e6881f83dfa6c7f8a3c3650ec2567e6b1e',
          html_url:
            'https://github.com/vuejs/core/commit/12ec62e6881f83dfa6c7f8a3c3650ec2567e6b1e',
        },
      ],
    },
  ],
  'v2-compat': [
    {
      sha: 'ecf4da822eea97f5db5fa769d39f994755384a4b',
      node_id:
        'MDY6Q29tbWl0MTM3MDc4NDg3OmVjZjRkYTgyMmVlYTk3ZjVkYjVmYTc2OWQzOWY5OTQ3NTUzODRhNGI=',
      commit: {
        author: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2018-11-13T03:42:34Z',
        },
        committer: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2018-11-13T03:54:01Z',
        },
        message: 'chore: fix tests',
        tree: {
          sha: '6ac7bd078a6eb0ad32b5102e0c5d2c29f2b20a48',
          url: 'https://api.github.com/repos/vuejs/core/git/trees/6ac7bd078a6eb0ad32b5102e0c5d2c29f2b20a48',
        },
        url: 'https://api.github.com/repos/vuejs/core/git/commits/ecf4da822eea97f5db5fa769d39f994755384a4b',
        comment_count: 0,
        verification: {
          verified: false,
          reason: 'unsigned',
          signature: null,
          payload: null,
        },
      },
      url: 'https://api.github.com/repos/vuejs/core/commits/ecf4da822eea97f5db5fa769d39f994755384a4b',
      html_url:
        'https://github.com/vuejs/core/commit/ecf4da822eea97f5db5fa769d39f994755384a4b',
      comments_url:
        'https://api.github.com/repos/vuejs/core/commits/ecf4da822eea97f5db5fa769d39f994755384a4b/comments',
      author: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      committer: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      parents: [
        {
          sha: 'ca296812d54aff123472d7147b83fddfb634d9bc',
          url: 'https://api.github.com/repos/vuejs/core/commits/ca296812d54aff123472d7147b83fddfb634d9bc',
          html_url:
            'https://github.com/vuejs/core/commit/ca296812d54aff123472d7147b83fddfb634d9bc',
        },
      ],
    },
    {
      sha: 'ca296812d54aff123472d7147b83fddfb634d9bc',
      node_id:
        'MDY6Q29tbWl0MTM3MDc4NDg3OmNhMjk2ODEyZDU0YWZmMTIzNDcyZDcxNDdiODNmZGRmYjYzNGQ5YmM=',
      commit: {
        author: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2018-11-13T03:21:56Z',
        },
        committer: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2018-11-13T03:46:06Z',
        },
        message: 'refactor: bring back clone for reused nodes',
        tree: {
          sha: '2cec32c97686e0ee9af1b87f0abdbbbdc18b6de6',
          url: 'https://api.github.com/repos/vuejs/core/git/trees/2cec32c97686e0ee9af1b87f0abdbbbdc18b6de6',
        },
        url: 'https://api.github.com/repos/vuejs/core/git/commits/ca296812d54aff123472d7147b83fddfb634d9bc',
        comment_count: 0,
        verification: {
          verified: false,
          reason: 'unsigned',
          signature: null,
          payload: null,
        },
      },
      url: 'https://api.github.com/repos/vuejs/core/commits/ca296812d54aff123472d7147b83fddfb634d9bc',
      html_url:
        'https://github.com/vuejs/core/commit/ca296812d54aff123472d7147b83fddfb634d9bc',
      comments_url:
        'https://api.github.com/repos/vuejs/core/commits/ca296812d54aff123472d7147b83fddfb634d9bc/comments',
      author: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      committer: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      parents: [
        {
          sha: 'e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
          url: 'https://api.github.com/repos/vuejs/core/commits/e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
          html_url:
            'https://github.com/vuejs/core/commit/e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
        },
      ],
    },
    {
      sha: 'e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
      node_id:
        'MDY6Q29tbWl0MTM3MDc4NDg3OmU2YmU1NWE0OTg5ZWRiNmY4NzUwZGJhYTE0ZWI2OTNlYzFmMGQ2N2I=',
      commit: {
        author: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2018-11-02T20:59:45Z',
        },
        committer: {
          name: 'Evan You',
          email: '<EMAIL>',
          date: '2018-11-02T20:59:45Z',
        },
        message: 'chore: relax render type for tsx',
        tree: {
          sha: '7e2b3bb92ab91f755b2251e4a7903e6dd2042602',
          url: 'https://api.github.com/repos/vuejs/core/git/trees/7e2b3bb92ab91f755b2251e4a7903e6dd2042602',
        },
        url: 'https://api.github.com/repos/vuejs/core/git/commits/e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
        comment_count: 0,
        verification: {
          verified: false,
          reason: 'unsigned',
          signature: null,
          payload: null,
        },
      },
      url: 'https://api.github.com/repos/vuejs/core/commits/e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
      html_url:
        'https://github.com/vuejs/core/commit/e6be55a4989edb6f8750dbaa14eb693ec1f0d67b',
      comments_url:
        'https://api.github.com/repos/vuejs/core/commits/e6be55a4989edb6f8750dbaa14eb693ec1f0d67b/comments',
      author: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      committer: {
        login: 'yyx990803',
        id: 499550,
        node_id: 'MDQ6VXNlcjQ5OTU1MA==',
        avatar_url: 'https://avatars1.githubusercontent.com/u/499550?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/yyx990803',
        html_url: 'https://github.com/yyx990803',
        followers_url: 'https://api.github.com/users/yyx990803/followers',
        following_url:
          'https://api.github.com/users/yyx990803/following{/other_user}',
        gists_url: 'https://api.github.com/users/yyx990803/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/yyx990803/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/yyx990803/subscriptions',
        organizations_url: 'https://api.github.com/users/yyx990803/orgs',
        repos_url: 'https://api.github.com/users/yyx990803/repos',
        events_url: 'https://api.github.com/users/yyx990803/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/yyx990803/received_events',
        type: 'User',
        site_admin: false,
      },
      parents: [
        {
          sha: 'ccc835caff0344baad3c92ce786ad4f804bf667b',
          url: 'https://api.github.com/repos/vuejs/core/commits/ccc835caff0344baad3c92ce786ad4f804bf667b',
          html_url:
            'https://github.com/vuejs/core/commit/ccc835caff0344baad3c92ce786ad4f804bf667b',
        },
      ],
    },
  ],
} as any
