<script src="../../dist/vue.global.js"></script>

<div><span id="custom-trigger">click here to hydrate</span></div>
<div id="app"><button>0</button></div>

<script>
  window.isHydrated = false
  const { createSSRApp, defineAsyncComponent, h, ref, onMounted } = Vue

  const Comp = {
    setup() {
      const count = ref(0)
      onMounted(() => {
        console.log('hydrated')
        window.isHydrated = true
      })
      return () => {
        return h('button', { onClick: () => count.value++ }, count.value)
      }
    },
  }

  const AsyncComp = defineAsyncComponent({
    loader: () => Promise.resolve(Comp),
    hydrate: (hydrate, el) => {
      const triggerEl = document.getElementById('custom-trigger')
      triggerEl.addEventListener('click', hydrate, { once: true })
      return () => {
        window.teardownCalled = true
        triggerEl.removeEventListener('click', hydrate)
      }
    },
  })

  const show = (window.show = ref(true))
  createSSRApp({
    setup() {
      onMounted(() => {
        window.isRootMounted = true
      })
      return () => (show.value ? h(AsyncComp) : 'off')
    },
  }).mount('#app')
</script>
