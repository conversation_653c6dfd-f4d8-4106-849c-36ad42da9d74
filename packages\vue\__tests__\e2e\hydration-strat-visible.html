<script src="../../dist/vue.global.js"></script>

<div style="height: 1000px">scroll to the bottom to hydrate</div>
<div id="app"><button>0</button></div>
<style>
  body {
    margin: 0;
  }
</style>

<script>
  const rootMargin = location.search.match(/rootMargin=(\d+)/)?.[1] ?? 0
  const isFragment = location.search.includes('?fragment')
  const isVIf = location.search.includes('?v-if')
  if (isFragment) {
    document.getElementById('app').innerHTML =
      `<!--[--><!--[--><span>one</span><!--]--><button>0</button><span>two</span><!--]-->`
  } else if (isVIf) {
    document.getElementById('app').innerHTML = `<!---->`
  }

  window.isHydrated = false
  const {
    createSSRApp,
    defineAsyncComponent,
    h,
    ref,
    onMounted,
    hydrateOnVisible,
    createCommentVNode,
  } = Vue

  const Comp = {
    setup() {
      const count = ref(0)
      onMounted(() => {
        console.log('hydrated')
        window.isHydrated = true
      })
      return () => {
        const button = h(
          'button',
          { onClick: () => count.value++ },
          count.value,
        )
        if (isVIf) {
          return createCommentVNode('v-if', true)
        } else if (isFragment) {
          return [[h('span', 'one')], button, h('span', 'two')]
        } else {
          return button
        }
      }
    },
  }

  const AsyncComp = defineAsyncComponent({
    loader: () => Promise.resolve(Comp),
    hydrate: hydrateOnVisible({ rootMargin: rootMargin + 'px' }),
  })

  createSSRApp({
    setup() {
      onMounted(() => {
        window.isRootMounted = true
      })
      return () => h(AsyncComp)
    },
  }).mount('#app')
</script>
