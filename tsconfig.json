{"compilerOptions": {"baseUrl": ".", "outDir": "temp", "sourceMap": false, "target": "es2016", "newLine": "LF", "useDefineForClassFields": false, "module": "esnext", "moduleResolution": "bundler", "allowJs": false, "strict": true, "noUnusedLocals": true, "experimentalDecorators": true, "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true, "esModuleInterop": true, "removeComments": false, "jsx": "preserve", "lib": ["es2016", "dom"], "types": ["vitest/globals", "puppeteer", "node"], "rootDir": ".", "paths": {"@vue/compat": ["packages/vue-compat/src"], "@vue/*": ["packages/*/src"], "vue": ["packages/vue/src"]}, "isolatedDeclarations": true, "composite": true}, "include": ["packages/global.d.ts", "packages/*/src", "packages/*/__tests__", "packages/vue/jsx-runtime", "packages/runtime-dom/types/jsx.d.ts", "scripts/*", "rollup.*.js"], "exclude": ["packages-private/sfc-playground/src/vue-dev-proxy*"]}