// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`compiler: codeframe > invalid start and end 1`] = `
"1  |  <div>
   |  ^
2  |    <template key="one"></template>
3  |    <ul>"
`;

exports[`compiler: codeframe > invalid start and end 2`] = `
"1  |  <div>
   |  ^^^^^
2  |    <template key="one"></template>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
3  |    <ul>
   |  ^^^^^^
4  |      <li v-for="foobar">hi</li>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
5  |    </ul>
   |  ^^^^^^^
6  |    <template key="two"></template>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7  |  </div>
   |  ^^^^^^"
`;

exports[`compiler: codeframe > invalid start and end 3`] = `""`;

exports[`compiler: codeframe > line in middle 1`] = `
"2  |    <template key="one"></template>
3  |    <ul>
4  |      <li v-for="foobar">hi</li>
   |          ^^^^^^^^^^^^^^
5  |    </ul>
6  |    <template key="two"></template>"
`;

exports[`compiler: codeframe > line near bottom 1`] = `
"4  |      <li v-for="foobar">hi</li>
5  |    </ul>
6  |    <template key="two"></template>
   |              ^^^^^^^^^
7  |  </div>"
`;

exports[`compiler: codeframe > line near top 1`] = `
"1  |  <div>
2  |    <template key="one"></template>
   |              ^^^^^^^^^
3  |    <ul>
4  |      <li v-for="foobar">hi</li>"
`;

exports[`compiler: codeframe > multi-line highlights 1`] = `
"1  |  <div attr="some
   |       ^^^^^^^^^^
2  |    multiline
   |  ^^^^^^^^^^^
3  |  attr
   |  ^^^^
4  |  ">
   |  ^"
`;

exports[`compiler: codeframe > newline sequences - unix 1`] = `
"8  |          <input name="email" type="text"/>
9  |        </div>
10 |        <div id="hook">
   |        ^^^^^^^^^^^^^^^
11 |          <label for="password">Password</label>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
12 |          <input name="password" type="password"/>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13 |        </div>
   |  ^^^^^^^^^^^^"
`;

exports[`compiler: codeframe > newline sequences - windows 1`] = `
"8  |          <input name="email" type="text"/>
9  |        </div>
10 |        <div id="hook">
   |        ^^^^^^^^^^^^^^^
11 |          <label for="password">Password</label>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
12 |          <input name="password" type="password"/>
   |  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13 |        </div>
   |  ^^^^^^^^^^^^"
`;
