{"private": true, "version": "3.5.12", "packageManager": "pnpm@9.12.0", "type": "module", "scripts": {"dev": "node scripts/dev.js", "build": "node scripts/build.js", "build-dts": "tsc -p tsconfig.build.json --noCheck && rollup -c rollup.dts.config.js", "clean": "rimraf --glob packages/*/dist temp .eslintcache", "size": "run-s \"size-*\" && node scripts/usage-size.js", "size-global": "node scripts/build.js vue runtime-dom -f global -p --size", "size-esm-runtime": "node scripts/build.js vue -f esm-bundler-runtime", "size-esm": "node scripts/build.js runtime-dom runtime-core reactivity shared -f esm-bundler", "check": "tsc --incremental --noEmit", "lint": "eslint --cache .", "format": "prettier --write --cache .", "format-check": "prettier --check --cache .", "test": "vitest", "test-unit": "vitest --project unit", "test-e2e": "node scripts/build.js vue -f global -d && vitest --project e2e", "test-dts": "run-s build-dts test-dts-only", "test-dts-only": "tsc -p packages-private/dts-built-test/tsconfig.json && tsc -p ./packages-private/dts-test/tsconfig.test.json", "test-coverage": "vitest run --project unit --coverage", "test-bench": "vitest bench", "release": "node scripts/release.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "dev-esm": "node scripts/dev.js -if esm-bundler-runtime", "dev-compiler": "run-p \"dev template-explorer\" serve", "dev-sfc": "run-s dev-sfc-prepare dev-sfc-run", "dev-sfc-prepare": "node scripts/pre-dev-sfc.js || npm run build-all-cjs", "dev-sfc-serve": "vite packages-private/sfc-playground --host", "dev-sfc-run": "run-p \"dev compiler-sfc -f esm-browser\" \"dev vue -if esm-bundler-runtime\" \"dev vue -ipf esm-browser-runtime\" \"dev server-renderer -if esm-bundler\" dev-sfc-serve", "serve": "serve", "open": "open http://localhost:3000/packages-private/template-explorer/local.html", "build-sfc-playground": "run-s build-all-cjs build-runtime-esm build-browser-esm build-ssr-esm build-sfc-playground-self", "build-all-cjs": "node scripts/build.js vue runtime compiler reactivity shared -af cjs", "build-runtime-esm": "node scripts/build.js runtime reactivity shared -af esm-bundler && node scripts/build.js vue -f esm-bundler-runtime && node scripts/build.js vue -f esm-browser-runtime", "build-browser-esm": "node scripts/build.js runtime reactivity shared -af esm-bundler && node scripts/build.js vue -f esm-bundler && node scripts/build.js vue -f esm-browser", "build-ssr-esm": "node scripts/build.js compiler-sfc server-renderer -f esm-browser", "build-sfc-playground-self": "cd packages-private/sfc-playground && npm run build", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged && pnpm check", "commit-msg": "node scripts/verify-commit.js"}, "lint-staged": {"*.{js,json}": ["prettier --write"], "*.ts?(x)": ["eslint --fix", "prettier --parser=typescript --write"]}, "engines": {"node": ">=18.12.0"}, "devDependencies": {"@babel/parser": "catalog:", "@babel/types": "catalog:", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "5.0.4", "@swc/core": "^1.7.28", "@types/hash-sum": "^1.0.2", "@types/node": "^20.16.10", "@types/semver": "^7.5.8", "@types/serve-handler": "^6.1.4", "@vitest/coverage-v8": "^2.1.1", "@vue/consolidate": "1.0.0", "conventional-changelog-cli": "^5.0.0", "enquirer": "^2.4.1", "esbuild": "^0.24.0", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^9.12.0", "eslint-plugin-import-x": "^4.3.1", "@vitest/eslint-plugin": "^1.0.1", "estree-walker": "catalog:", "jsdom": "^25.0.0", "lint-staged": "^15.2.10", "lodash": "^4.17.21", "magic-string": "^0.30.11", "markdown-table": "^3.0.3", "marked": "13.0.3", "npm-run-all2": "^6.2.3", "picocolors": "^1.1.0", "prettier": "^3.3.3", "pretty-bytes": "^6.1.1", "pug": "^3.0.3", "puppeteer": "~23.3.0", "rimraf": "^6.0.1", "rollup": "^4.24.0", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-polyfill-node": "^0.13.0", "semver": "^7.6.3", "serve": "^14.2.3", "serve-handler": "^6.1.5", "simple-git-hooks": "^2.11.1", "todomvc-app-css": "^2.4.3", "tslib": "^2.7.0", "typescript": "~5.6.2", "typescript-eslint": "^8.8.0", "vite": "catalog:", "vitest": "^2.1.1"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"typescript-eslint>eslint": "^9.0.0", "@typescript-eslint/eslint-plugin>eslint": "^9.0.0", "@typescript-eslint/parser>eslint": "^9.0.0", "@typescript-eslint/type-utils>eslint": "^9.0.0", "@typescript-eslint/utils>eslint": "^9.0.0"}}}}