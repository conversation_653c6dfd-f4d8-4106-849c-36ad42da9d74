name: autofix.ci

on:
  pull_request:
permissions:
  contents: read

jobs:
  autofix:
    runs-on: ubuntu-latest
    env:
      PUPPETEER_SKIP_DOWNLOAD: 'true'
    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4.0.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'

      - run: pnpm install

      - name: Run eslint
        run: pnpm run lint --fix

      - name: Run prettier
        run: pnpm run format

      - uses: autofix-ci/action@ff86a557419858bb967097bfc916833f5647fa8c
