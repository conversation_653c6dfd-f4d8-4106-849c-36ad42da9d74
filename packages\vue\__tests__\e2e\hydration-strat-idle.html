<script src="../../dist/vue.global.js"></script>

<div id="app"><button>0</button></div>

<script>
  window.isHydrated = false
  const {
    createSSRApp,
    defineAsyncComponent,
    h,
    ref,
    onMounted,
    hydrateOnIdle,
  } = Vue

  const Comp = {
    setup() {
      const count = ref(0)
      onMounted(() => {
        console.log('hydrated')
        window.isHydrated = true
      })
      return () => h('button', { onClick: () => count.value++ }, count.value)
    },
  }

  const AsyncComp = defineAsyncComponent({
    loader: () =>
      new Promise(resolve => {
        setTimeout(() => {
          console.log('resolve')
          resolve(Comp)
          requestIdleCallback(() => {
            console.log('busy')
          })
        }, 10)
      }),
    hydrate: hydrateOnIdle(),
  })

  createSSRApp({
    render: () => h(AsyncComp),
  }).mount('#app')
</script>
