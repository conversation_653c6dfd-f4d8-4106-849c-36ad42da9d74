# Reporting a Vulnerability

To report a vulnerability, <NAME_EMAIL>.

While the discovery of new vulnerabilities is rare, we also recommend always using the latest versions of Vue and its official companion libraries to ensure your application remains as secure as possible.

Please note that we do not consider XSS via template expressions a valid attack vector, because it can only happen if the user intentionally uses untrusted content as template compilation source. This is similar to knowingly pasting untrusted scripts into a browser console. We explicitly warn users against using untrusted content as template compilation source in our documentation.

## Security Hall of Fame

We would like to thank the following security researchers for responsibly disclosing security issues to us.

- Jeet Pal - [@jeetpal2007](https://github.com/jeetpal2007) | [Email](mailto:<EMAIL>) | [LinkedIn](https://in.linkedin.com/in/jeet-pal-22601a290)
- Mix - [@mnixry](https://github.com/mnixry)
- <PERSON> - [@RedYetiDev](https://github.com/redyetidev) | [LinkedIn](https://www.linkedin.com/in/redyetidev) <<EMAIL>>
