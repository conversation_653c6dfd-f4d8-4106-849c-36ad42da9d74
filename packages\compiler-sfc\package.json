{"name": "@vue/compiler-sfc", "version": "3.5.12", "description": "@vue/compiler-sfc", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "types": "dist/compiler-sfc.d.ts", "files": ["dist"], "exports": {".": {"types": "./dist/compiler-sfc.d.ts", "node": "./dist/compiler-sfc.cjs.js", "module": "./dist/compiler-sfc.esm-browser.js", "import": "./dist/compiler-sfc.esm-browser.js", "require": "./dist/compiler-sfc.cjs.js"}, "./*": "./*"}, "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "dependencies": {"@babel/parser": "catalog:", "@vue/compiler-core": "workspace:*", "@vue/compiler-dom": "workspace:*", "@vue/compiler-ssr": "workspace:*", "@vue/shared": "workspace:*", "estree-walker": "catalog:", "magic-string": "catalog:", "postcss": "^8.4.47", "source-map-js": "catalog:"}, "devDependencies": {"@babel/types": "catalog:", "@vue/consolidate": "^1.0.0", "hash-sum": "^2.0.0", "lru-cache": "10.1.0", "merge-source-map": "^1.1.0", "minimatch": "~9.0.5", "postcss-modules": "^6.0.0", "postcss-selector-parser": "^6.1.2", "pug": "^3.0.3", "sass": "^1.79.4"}}