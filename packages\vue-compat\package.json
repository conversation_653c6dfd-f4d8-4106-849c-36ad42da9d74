{"name": "@vue/compat", "version": "3.5.12", "description": "Vue 3 compatibility build for Vue 2", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "unpkg": "dist/vue.global.js", "jsdelivr": "dist/vue.global.js", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/vue.d.ts", "node": {"production": "./dist/vue.cjs.prod.js", "development": "./dist/vue.cjs.js", "default": "./index.js"}, "module": "./dist/vue.esm-bundler.js", "import": "./dist/vue.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "buildOptions": {"name": "<PERSON><PERSON>", "filename": "vue", "compat": true, "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/vue-compat#readme", "dependencies": {"@babel/parser": "catalog:", "estree-walker": "catalog:", "source-map-js": "catalog:"}, "peerDependencies": {"vue": "workspace:*"}}