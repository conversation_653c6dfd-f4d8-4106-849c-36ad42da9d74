// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`defineEmits > basic usage 1`] = `
"
export default {
  emits: ['foo', 'bar'],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

const myEmit = __emit

return { myEmit }
}

}"
`;

exports[`defineEmits > w/ runtime options 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  emits: ['a', 'b'],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

const emit = __emit

return { emit }
}

})"
`;

exports[`defineEmits > w/ type (exported interface) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
export interface Emits { (e: 'foo' | 'bar'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (exported type alias) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
export type Emits = { (e: 'foo' | 'bar'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (interface ts type) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Emits { (e: 'foo'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ['foo'],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit: Emits = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (interface w/ extends) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Base { (e: 'foo'): void }
    interface Emits extends Base { (e: 'bar'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ["bar", "foo"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (interface) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Emits { (e: 'foo' | 'bar'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (property syntax string literal) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  emits: ["foo:bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (property syntax) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (referenced exported function type) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
export type Emits = (e: 'foo' | 'bar') => void
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (referenced function type) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type Emits = (e: 'foo' | 'bar') => void
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (type alias) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type Emits = { (e: 'foo' | 'bar'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (type literal w/ call signatures) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar", "baz"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (type references in union) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type BaseEmit = "change"
    type Emit = "some" | "emit" | BaseEmit
    
export default /*@__PURE__*/_defineComponent({
  emits: ["some", "emit", "change", "another"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit;
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type (union) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar", "baz"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;

exports[`defineEmits > w/ type from normal script 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

      export interface Emits { (e: 'foo' | 'bar'): void }
    
export default /*@__PURE__*/_defineComponent({
  emits: ["foo", "bar"],
  setup(__props, { expose: __expose, emit: __emit }) {
  __expose();

    const emit = __emit
    
return { emit }
}

})"
`;
