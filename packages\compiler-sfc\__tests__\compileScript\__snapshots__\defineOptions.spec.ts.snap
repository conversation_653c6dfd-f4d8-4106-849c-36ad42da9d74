// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`defineOptions() > basic usage 1`] = `
"
export default /*@__PURE__*/Object.assign({ name: '<PERSON>oo<PERSON><PERSON>' }, {
  setup(__props, { expose: __expose }) {
  __expose();

      
      
return {  }
}

})"
`;

exports[`defineOptions() > empty argument 1`] = `
"
export default {
  setup(__props, { expose: __expose }) {
  __expose();

      
      
return {  }
}

}"
`;
