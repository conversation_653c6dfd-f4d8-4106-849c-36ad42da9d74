<script src="../../../../node_modules/marked/marked.min.js"></script>
<script src="../../../../node_modules/lodash/lodash.min.js"></script>
<script src="../../dist/vue.global.js"></script>

<div id="editor">
  <textarea :value="input" @input="update"></textarea>
  <div v-html="compiledMarkdown"></div>
</div>

<script>
  Vue.createApp({
    data: () => ({
      input: '# hello',
    }),
    computed: {
      compiledMarkdown() {
        return marked.marked(this.input, { sanitize: true })
      },
    },
    methods: {
      update: _.debounce(function (e) {
        this.input = e.target.value
      }, 50),
    },
  }).mount('#editor')
</script>

<style>
  html,
  body,
  #editor {
    margin: 0;
    height: 100%;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    color: #333;
  }

  textarea,
  #editor div {
    display: inline-block;
    overflow: auto;
    width: 50%;
    height: 100%;
    vertical-align: top;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 20px;
  }

  textarea {
    border: none;
    border-right: 1px solid #ccc;
    resize: none;
    outline: none;
    background-color: #f6f6f6;
    font-size: 14px;
    font-family: 'Monaco', courier, monospace;
    padding: 20px;
  }

  code {
    color: #f66;
  }
</style>
